<!DOCTYPE html>
<html>
<head>
    <title>指标仓库</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css?v=1.3" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0" />
    <style>
        .vue-box {
            padding: 0;
            height: 100%;
            box-sizing: border-box;
            background-color: #f5f7fa;
        }
        .drawer-content .el-date-editor {
            width: 100%;
        }
    </style>
</head>

<body class="yq-page-full vue-box">
    <div id="metricsTable" class="flex yq-table-page" v-loading="loading" :element-loading-text="getI18nValue('加载中...')" v-cloak>
        <div class="yq-card">
            <div class="card-header">
                <div class="head-title">{{ getI18nValue('指标仓库') }}</div>
                <div class="yq-table-control">
                    <el-button type="primary" size="small" @click="handleAdd">
                        <i class="el-icon-plus"></i>{{ getI18nValue('新增') }}
                    </el-button>
                </div>
            </div>
            <div class="card-content">
                <senior-search :show.sync="moreSearch">
                    <el-form class="search-form" :inline="false" :model="searchForm" ref="searchForm" size="small" label-width="120px">
                        <el-form-item :label="getI18nValue('指标名称')" prop="metrics">
                            <el-input v-model="searchForm.metrics" :placeholder="getI18nValue('请输入')" clearable></el-input>
                        </el-form-item>
                        <el-form-item :label="getI18nValue('指标类型')" prop="metricsType">
                            <el-select v-model="searchForm.metricsType" clearable :placeholder="getI18nValue('请选择')">
                                <el-option
                                    v-for="item in dictList.YG_METRICS_TYPE"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="getI18nValue('指标来源')" prop="metricsSource">
                            <el-select v-model="searchForm.metricsSource" clearable :placeholder="getI18nValue('请选择')">
                                <el-option
                                    v-for="item in dictList.YG_METRICS_SOURCE"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item class="btns" label-width="0px">
                            <el-button type="primary" plain size="small" icon="el-icon-refresh" @click="handleReset">{{getI18nValue('重置')}}</el-button>
                            <el-button type="primary" size="small" icon="el-icon-search" @click="getList(1)">{{getI18nValue('搜索')}}</el-button>
                        </el-form-item>
                    </el-form>
                </senior-search>

                <div class="yq-table">
                    <el-table stripe :data="tableData.data" height="100%" fit ref="table" style="width: 100%">
                        <el-table-column :label="getI18nValue('序号')" type="index" width="90px" fixed>
                            <template slot-scope="scope">
                                {{ (tableData.pageIndex - 1) * tableData.pageSize + scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="metrics" :label="getI18nValue('指标名称')" show-overflow-tooltip min-width="180"></el-table-column>
                        <el-table-column prop="metricsType" :label="getI18nValue('指标类型')" min-width="120">
                            <template slot-scope="scope">
                                {{getDictName('YG_METRICS_TYPE', scope.row.metricsType)}}
                            </template>
                        </el-table-column>
                        <el-table-column prop="metricsSource" :label="getI18nValue('指标来源')" min-width="120">
                            <template slot-scope="scope">
                                {{getDictName('YG_METRICS_SOURCE', scope.row.metricsSource)}}
                            </template>
                        </el-table-column>
                        <el-table-column :label="getI18nValue('操作')" min-width="200" fixed="right">
                            <template slot-scope="scope">
                                <el-link @click="handleEdit(scope.row)" type="primary" :underline="false">{{getI18nValue('修改')}}</el-link>
                                <el-link @click="handleDel(scope.row)" type="danger" :underline="false">{{getI18nValue('删除')}}</el-link>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination background @current-change="onPageChange" @size-change="onPageSizeChange"
                        :current-page="tableData.pageIndex" :page-size="tableData.pageSize" :page-sizes="[15, 30, 50, 100]"
                        layout="total, prev, pager, next, jumper,sizes" :total="tableData.totalRow">
                    </el-pagination>
                </div>
            </div>
        </div>

        <el-drawer custom-class="yq-drawer" :title="form.id ? getI18nValue('修改指标') : getI18nValue('新增指标')" :visible.sync="drawer"
            direction="rtl" size="35%" :wrapper-closable="false" @close="closeDrawer">
            <div class="drawer-content yq-drawer-content">
                <el-form label-width="150px" ref="ruleForm" :model="form" :rules="rules">
                    <el-form-item :label="getI18nValue('指标名称')" prop="metrics">
                        <el-input :placeholder="getI18nValue('请输入')" v-model="form.metrics"></el-input>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('指标类型')" prop="metricsType">
                        <el-select v-model="form.metricsType" :placeholder="getI18nValue('请选择')">
                            <el-option
                                v-for="item in dictList.YG_METRICS_TYPE"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('指标来源')" prop="metricsSource">
                        <el-select v-model="form.metricsSource" :placeholder="getI18nValue('请选择')" @change="handleSourceChange">
                            <el-option
                                v-for="item in dictList.YG_METRICS_SOURCE"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 数据库配置 -->
                    <div v-if="form.metricsSource === '1'" class="config-form">
                        <el-form-item :label="getI18nValue('统计表')" prop="dbConfig.tableName">
                            <el-select v-model="dbConfig.tableName" :placeholder="getI18nValue('请选择')" @change="handleTableChange">
                                <el-option
                                    v-for="item in tableList"
                                    :key="item.tableName"
                                    :label="item.tableName"
                                    :value="item.tableName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="getI18nValue('统计字段')" prop="dbConfig.columnName">
                            <el-select v-model="dbConfig.columnName" :placeholder="getI18nValue('请选择')">
                                <el-option
                                    v-for="item in columnList"
                                    :key="item.columnName"
                                    :label="item.columnName"
                                    :value="item.columnName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>

                    <!-- Redis配置 -->
                    <div v-if="form.metricsSource === '2'" class="config-form">
                        <el-form-item :label="getI18nValue('Redis Key')" prop="redisConfig.key">
                            <el-input :placeholder="getI18nValue('请输入')" v-model="redisConfig.key"></el-input>
                        </el-form-item>
                    </div>

                    <!-- 内置程序配置 -->
                    <div v-if="form.metricsSource === '3'" class="config-form">
                        <el-form-item :label="getI18nValue('程序类名')" prop="programConfig.className">
                            <el-input :placeholder="getI18nValue('请输入')" v-model="programConfig.className"></el-input>
                        </el-form-item>
                        <el-form-item :label="getI18nValue('方法名')" prop="programConfig.methodName">
                            <el-input :placeholder="getI18nValue('请输入')" v-model="programConfig.methodName"></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <div class="drawer-footer yq-drawer-footer">
                <el-button type="primary" plain @click="drawer=false">{{ getI18nValue('取消') }}</el-button>
                <el-button type="primary" :loading="isSave" @click="handleSubmit" style="margin-left: 16px">{{getI18nValue('确定')}}</el-button>
            </div>
        </el-drawer>
    </div>
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
<script src="/cc-base/static/js/i18n.js?v=1"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
<script>
    var appPage = new Vue({
        el: "#metricsTable",
        data: function () {
            return {
                loading: false,
                isSave: false,
                drawer: false,
                moreSearch: false,
                tableList: [],
                columnList: [],
                searchForm: {
                    metrics: '',
                    metricsType: '',
                    metricsSource: ''
                },
                form: {
                    id: "",
                    metrics: '',
                    metricsType: '',
                    metricsSource: '',
                    metricsConfig: ''
                },
                dbConfig: {
                    tableName: '',
                    columnName: ''
                },
                redisConfig: {
                    key: ''
                },
                programConfig: {
                    className: '',
                    methodName: ''
                },
                tableData: {
                    pageIndex: 1,
                    pageSize: 15,
                    totalRow: 0,
                    data: []
                },
                dictList: {},
                rules: {
                    metrics: [{ required: true, message: '指标名称不能为空', trigger: 'blur' }],
                    metricsType: [{ required: true, message: '指标类型不能为空', trigger: 'change' }],
                    metricsSource: [{ required: true, message: '指标来源不能为空', trigger: 'change' }]
                }
            };
        },
        created() {
            this.getDictList();
            this.getList(); // 加载初始数据
        },
        methods: {
            handleSourceChange(value) {
                this.resetConfig(); // 重置配置
                if (value === '1') {
                    this.getStatTables(); // 加载统计表
                }
                // 其他数据来源的处理
                else if (value === '2') {
                    // Redis相关配置
                    this.redisConfig = { key: '' };
                }
                else if (value === '3') {
                    // 程序相关配置
                    this.programConfig = { className: '', methodName: '' };
                }
            },
            resetConfig() {
                this.dbConfig = { tableName: '', columnName: '' };
                this.redisConfig = { key: '' };
                this.programConfig = { className: '', methodName: '' };
            },
            getStatTables() {
                this.remoteCall("/cc-employee/webcall?action=MetricsWareDao.getStatTables", {}, (res) => {
                    if (res.state == 1) {
                        this.tableList = res.data;
                    } else {
                        this.showError(res.msg);
                    }
                });
            },
            getTableColumns(tableName) {
                this.remoteCall("/cc-employee/webcall?action=MetricsWareDao.getTableColumns", { tableName }, (res) => {
                    if (res.state == 1) {
                        this.columnList = res.data;
                    } else {
                        this.showError(res.msg);
                    }
                });
            },
            handleEdit(data) {
                this.drawer = true;
                this.form = { ...data };
                this.parseConfig(data.metricsConfig, data.metricsSource);
            },
            parseConfig(configString, source) {
                try {
                    const config = JSON.parse(configString || '{}');
                    if (source === '1') {
                        this.dbConfig = config;
                        this.getStatTables();
                        if (config.tableName) {
                            this.getTableColumns(config.tableName);
                        }
                    } else if (source === '2') {
                        this.redisConfig = config;
                    } else if (source === '3') {
                        this.programConfig = config;
                    }
                } catch (e) {
                    console.error('解析配置信息失败:', e);
                }
            },
            closeDrawer() {
                this.resetForm();
                this.resetConfig();
                this.$refs["ruleForm"].resetFields();
            },
            resetForm() {
                this.form = { id: "", metrics: '', metricsType: '', metricsSource: '', metricsConfig: '' };
            },
            handleReset() {
                this.$refs.searchForm.resetFields();
            },
            handleSubmit() {
                this.$refs["ruleForm"].validate((valid) => {
                    if (valid) {
                        const config = this.prepareConfig();
                        if (config) {
                            this.form.metricsConfig = JSON.stringify(config);
                            this.save();
                        }
                    }
                });
            },
            prepareConfig() {
                let config = {};
                if (this.form.metricsSource === '1') {
                    if (!this.dbConfig.tableName || !this.dbConfig.columnName) {
                        this.showError('请完善数据库配置信息');
                        return null;
                    }
                    config = this.dbConfig;
                } else if (this.form.metricsSource === '2') {
                    if (!this.redisConfig.key) {
                        this.showError('请输入Redis Key');
                        return null;
                    }
                    config = this.redisConfig;
                } else if (this.form.metricsSource === '3') {
                    if (!this.programConfig.className || !this.programConfig.methodName) {
                        this.showError('请完善程序配置信息');
                        return null;
                    }
                    config = this.programConfig;
                }
                return config;
            },
            handleDel(data) {
                this.$confirm('是否删除该指标?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.delApi({ id: data.id });
                });
            },
            handleAdd() {
                this.drawer = true;
                this.resetForm();
            },
            getList(page = 1) {
                this.loading = true;
                this.tableData.pageIndex = page;
                const data = { pageIndex: this.tableData.pageIndex, pageSize: this.tableData.pageSize, ...this.searchForm };
                this.remoteCall("/cc-employee/webcall?action=MetricsWareDao.getMetricsList", data, (res) => {
                    this.loading = false;
                    if (res.state == 1) {
                        this.tableData.data = res.data;
                        this.tableData.totalRow = res.totalRow;
                    } else {
                        this.showError(res.msg);
                    }
                });
            },
            save() {
                this.isSave = true;
                this.remoteCall("/cc-employee/servlet/metricsWareServlet?action=editMetrics", { ...this.form }, (data) => {
                    this.isSave = false;
                    this.$message({
                        message: data.msg,
                        type: data.state == 1 ? "success" : "error"
                    });
                    if (data.state == 1) {
                        this.drawer = false;
                        this.getList();
                    }
                });
            },
            delApi(data) {
                this.remoteCall("/cc-employee/servlet/metricsWareServlet?action=delMetrics", data, (res) => {
                    this.showMessage(res);
                    if (res.state == 1) {
                        this.getList();
                    }
                });
            },
            getDictList() {
                yq.daoCall(
                    { "controls": ["common.getDict(YG_METRICS_TYPE)", "common.getDict(YG_METRICS_SOURCE)"], "params": {} },
                    (data) => {
                        this.dictList["YG_METRICS_TYPE"] = data["common.getDict(YG_METRICS_TYPE)"].list;
                        this.dictList["YG_METRICS_SOURCE"] = data["common.getDict(YG_METRICS_SOURCE)"].list;
                    },
                    { contextPath: 'cc-employee' }
                );
            },
            onPageChange(page) {
                this.getList(page);
            },
            onPageSizeChange(size) {
                this.tableData.pageSize = size;
                this.getList(1);
            },
            showError(message) {
                this.$message.error(this.getI18nValue(message));
            },
            showMessage(res) {
                this.$message({
                    message: res.msg,
                    type: res.state == 1 ? "success" : "error"
                });
            },
            remoteCall(url, params, callback) {
                yq.remoteCall(url, params, callback).catch(() => {
                    this.loading = false;
                    this.showError('请求失败');
                });
            },
            getI18nValue(key) {
                return getI18nValue(key);
            },
            getDictName(type, value) {
                if (!this.dictList[type]) return '';
                const item = this.dictList[type].find(item => item.code === value);
                return item ? item.name : '';
            },
            handleTableChange(tableName) {
                if (tableName) {
                    this.getTableColumns(tableName);
                }
            }
        },
        mounted() {
            this.getList();
        }
    });
</script>

</html> 