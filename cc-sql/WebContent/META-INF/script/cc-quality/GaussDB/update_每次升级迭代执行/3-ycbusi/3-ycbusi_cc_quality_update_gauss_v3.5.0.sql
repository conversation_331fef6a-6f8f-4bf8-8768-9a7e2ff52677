-- 20240708
------ 为CC_QC_TASK_LOG表添加EX_JSON列
ALTER TABLE CC_QC_TASK_LOG ADD COLUMN EX_JSON TEXT;
COMMENT ON COLUMN CC_QC_TASK_LOG.EX_JSON IS '质检任务执行日志';

-- 20240718
---- 为CC_QC_THIRD_RECORD表添加OBJECT_ID列
ALTER TABLE CC_QC_THIRD_RECORD ADD COLUMN OBJECT_ID VARCHAR(64);
COMMENT ON COLUMN CC_QC_THIRD_RECORD.OBJECT_ID IS '业务id';

---- 为CC_QC_RESULT表添加CACH_STATE列
ALTER TABLE CC_QC_RESULT ADD COLUMN CACH_STATE INT DEFAULT 0;
COMMENT ON COLUMN CC_QC_RESULT.CACH_STATE IS '0-无需辅导 1-待辅导 2-辅导结束';

---- 为CC_QC_RESULT表添加CACH_BAKUP列
ALTER TABLE CC_QC_RESULT ADD COLUMN CACH_BAKUP VARCHAR(2000);
COMMENT ON COLUMN CC_QC_RESULT.CACH_BAKUP IS '辅导员填写的辅导结果备注信息';

---- 为CC_QC_RESULT表添加CACH_ACC列
ALTER TABLE CC_QC_RESULT ADD COLUMN CACH_ACC VARCHAR(30);
COMMENT ON COLUMN CC_QC_RESULT.CACH_ACC IS '辅导人员';

---- 为CC_QC_RESULT表添加CACH_TIME列
ALTER TABLE CC_QC_RESULT ADD COLUMN CACH_TIME VARCHAR(19);
COMMENT ON COLUMN CC_QC_RESULT.CACH_TIME IS '辅导时间';

-- 20240816
---- 为CC_QC_LABEL表添加START_SCORE和END_SCORE列
ALTER TABLE CC_QC_LABEL ADD COLUMN START_SCORE NUMERIC(10);
ALTER TABLE CC_QC_LABEL ADD COLUMN END_SCORE NUMERIC(10);
COMMENT ON COLUMN CC_QC_LABEL.START_SCORE IS '开始分数';
COMMENT ON COLUMN CC_QC_LABEL.END_SCORE IS '截止分数';

-- 20240823
---- 为cc_qc_task_obj表创建索引
CREATE INDEX IDX_CC_QC_TASK_OBJ_16 ON cc_qc_task_obj (SERVICE_TIME);

---- 为CC_QC_TASK_OBJ表添加ZN_RECONSIDER_FLAG列
ALTER TABLE CC_QC_TASK_OBJ ADD COLUMN ZN_RECONSIDER_FLAG INT DEFAULT 0;
COMMENT ON COLUMN CC_QC_TASK_OBJ.ZN_RECONSIDER_FLAG IS '0-未申诉 1-已申诉';

---- 修改CC_QC_TASK_OBJ表的ZN_RESULT列
ALTER TABLE CC_QC_TASK_OBJ ALTER COLUMN ZN_RESULT TYPE VARCHAR(1000);
COMMENT ON COLUMN CC_QC_TASK_OBJ.ZN_RESULT IS '智能质检结果';

-- 20241017
---- 为CC_QC_TASK_OBJ表创建索引
CREATE INDEX IDX_CC_QC_TASK_OBJ16 ON CC_QC_TASK_OBJ (CREATE_TIME, ENT_ID, BUSI_ORDER_ID);


-- 增加业务类型字段 新版本兼容旧报表
-- 添加列BUSI_TYPE到表CC_QC_TASK，并设置数据类型为VARCHAR(10)，添加注释
ALTER TABLE CC_QC_TASK ADD COLUMN BUSI_TYPE VARCHAR(10);
COMMENT ON COLUMN CC_QC_TASK.BUSI_TYPE IS '业务类型,1:语音 2:全媒体 3:邮件 4:工单';

-- 添加列BUSI_TYPE到表CC_QC_TASK_OBJ，并设置数据类型为VARCHAR(10)，添加注释
ALTER TABLE CC_QC_TASK_OBJ ADD COLUMN BUSI_TYPE VARCHAR(10);
COMMENT ON COLUMN CC_QC_TASK_OBJ.BUSI_TYPE IS '业务类型,1:语音 2:全媒体 3:邮件 4:工单';

---- 为统计表增加业务类型字段
ALTER TABLE stat.C_ST_QC_EXTRACT_STAT1 ADD COLUMN BUSI_TYPE VARCHAR(10);
COMMENT ON COLUMN stat.C_ST_QC_EXTRACT_STAT1.BUSI_TYPE IS '业务类型,1:语音 2:全媒体 3:邮件 4:工单';

ALTER TABLE stat.C_ST_QC_EXTRACT_STAT2 ADD COLUMN BUSI_TYPE VARCHAR(10);
COMMENT ON COLUMN stat.C_ST_QC_EXTRACT_STAT2.BUSI_TYPE IS '业务类型,1:语音 2:全媒体 3:邮件 4:工单';

ALTER TABLE stat.C_ST_QC_RG_STAT1 ADD COLUMN BUSI_TYPE VARCHAR(10);
COMMENT ON COLUMN stat.C_ST_QC_RG_STAT1.BUSI_TYPE IS '业务类型,1:语音 2:全媒体 3:邮件 4:工单';

ALTER TABLE stat.C_ST_QC_RG_STAT2 ADD COLUMN BUSI_TYPE VARCHAR(10);
COMMENT ON COLUMN stat.C_ST_QC_RG_STAT2.BUSI_TYPE IS '业务类型,1:语音 2:全媒体 3:邮件 4:工单';

---- 兼容运营中心，增加根企业id字段，并设置默认值为'0'
ALTER TABLE cc_qc_class ADD COLUMN P_ENT_ID VARCHAR(30) DEFAULT '0';
COMMENT ON COLUMN cc_qc_class.P_ENT_ID IS '根企业id';

ALTER TABLE cc_qc_item ADD COLUMN P_ENT_ID VARCHAR(30) DEFAULT '0';
COMMENT ON COLUMN cc_qc_item.P_ENT_ID IS '根企业id';


-- 20241024
---- 新增智能质检结果-人工复检接口相关字段
---- 为cc_qc_capacity表添加BASE_SCORE列
ALTER TABLE cc_qc_capacity ADD COLUMN BASE_SCORE VARCHAR(10);
COMMENT ON COLUMN cc_qc_capacity.BASE_SCORE IS '基础分';

---- 为cc_qc_capacity表添加IS_REVIEW列
ALTER TABLE cc_qc_capacity ADD COLUMN IS_REVIEW INT DEFAULT 0;
COMMENT ON COLUMN cc_qc_capacity.IS_REVIEW IS '是否复检，0-否 1-是';

---- 为cc_qc_capacity表添加RECORD_ID列
ALTER TABLE cc_qc_capacity ADD COLUMN RECORD_ID VARCHAR(64);
COMMENT ON COLUMN cc_qc_capacity.RECORD_ID IS '智能质检-记录明细ID';

---- 为cc_qc_details表添加ZN_ID列
ALTER TABLE cc_qc_details ADD COLUMN ZN_ID VARCHAR(64);
COMMENT ON COLUMN cc_qc_details.ZN_ID IS '智能质检-质检项明细ID';

---- 为cc_qc_details表添加IS_VOTE列
ALTER TABLE cc_qc_details ADD COLUMN IS_VOTE INT;
COMMENT ON COLUMN cc_qc_details.IS_VOTE IS '是否一票否决，0-否 1-是';

---- 质检平台新增四张外部默认渠道表，提供外部系统数据接受标准接口
---- 为cc_qc_third_record表添加GROUP_ID列
ALTER TABLE cc_qc_third_record ADD COLUMN GROUP_ID VARCHAR(64);
COMMENT ON COLUMN cc_qc_third_record.GROUP_ID IS '技能组ID';

---- 为cc_qc_third_record表添加GROUP_NAME列
ALTER TABLE cc_qc_third_record ADD COLUMN GROUP_NAME VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.GROUP_NAME IS '技能组名称';

---- 为cc_qc_third_record表添加AGENT_ID列
ALTER TABLE cc_qc_third_record ADD COLUMN AGENT_ID VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.AGENT_ID IS '坐席工号';

---- 为cc_qc_third_record表添加CREATE_CAUSE列
ALTER TABLE cc_qc_third_record ADD COLUMN CREATE_CAUSE VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.CREATE_CAUSE IS '呼叫创建原因';

---- 为cc_qc_third_record表添加CLEAR_CAUSE列
ALTER TABLE cc_qc_third_record ADD COLUMN CLEAR_CAUSE VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.CLEAR_CAUSE IS '挂断类型';

---- 为cc_qc_third_record表添加QUEUE_STAY_TIME列
ALTER TABLE cc_qc_third_record ADD COLUMN QUEUE_STAY_TIME VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.QUEUE_STAY_TIME IS '排队时间';

---- 为cc_qc_third_record表添加CUST_CODE列
ALTER TABLE cc_qc_third_record ADD COLUMN CUST_CODE VARCHAR(64);
COMMENT ON COLUMN cc_qc_third_record.CUST_CODE IS '客户唯一标识';

---- 为cc_qc_third_record表添加CUST_NAME列
ALTER TABLE cc_qc_third_record ADD COLUMN CUST_NAME VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.CUST_NAME IS '客户名称';

---- 为cc_qc_third_record表添加ORDER_ID列
ALTER TABLE cc_qc_third_record ADD COLUMN ORDER_ID VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.ORDER_ID IS '工单编号';

---- 为cc_qc_third_record表添加ORDER_TYPE列
ALTER TABLE cc_qc_third_record ADD COLUMN ORDER_TYPE VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.ORDER_TYPE IS '工单类型';

---- 为cc_qc_third_record表添加FLOW_NAME列
ALTER TABLE cc_qc_third_record ADD COLUMN FLOW_NAME VARCHAR(30);
COMMENT ON COLUMN cc_qc_third_record.FLOW_NAME IS '流程名称';

---- 为cc_qc_third_record表添加CREATOR列
ALTER TABLE cc_qc_third_record ADD COLUMN CREATOR VARCHAR(64);
COMMENT ON COLUMN cc_qc_third_record.CREATOR IS '记录创建人';

---- 为cc_qc_third_record表添加ORDER_CONTENT列
ALTER TABLE cc_qc_third_record ADD COLUMN ORDER_CONTENT VARCHAR(4000);
COMMENT ON COLUMN cc_qc_third_record.ORDER_CONTENT IS '工单内容';

---- 为cc_qc_third_record表添加ORDER_FINISH_TIME列
ALTER TABLE cc_qc_third_record ADD COLUMN ORDER_FINISH_TIME VARCHAR(20);
COMMENT ON COLUMN cc_qc_third_record.ORDER_FINISH_TIME IS '工单办结时间';

---- 为cc_qc_third_record表添加INBOUND_TIME列
ALTER TABLE cc_qc_third_record ADD COLUMN INBOUND_TIME VARCHAR(20);
COMMENT ON COLUMN cc_qc_third_record.INBOUND_TIME IS '数据入库时间';

---- 为cc_qc_third_record表添加DATA_DETAIL_URL列
ALTER TABLE cc_qc_third_record ADD COLUMN DATA_DETAIL_URL VARCHAR(255);
COMMENT ON COLUMN cc_qc_third_record.DATA_DETAIL_URL IS '质检详情数据展示URL';

---- 创建表cc_qc_third_record_info
CREATE TABLE cc_qc_third_record_info
(
    ID VARCHAR(64) NOT NULL,
    SERIAL_ID VARCHAR(64) NOT NULL,
    MSG_TIME VARCHAR(20),
    CHANNEL VARCHAR(10),
    MSG_TYPE VARCHAR(10),
    MSG_CONTENT TEXT,
    CREATE_TIME VARCHAR(20),
    PRIMARY KEY (ID)
);
---- 添加列的注释
COMMENT ON COLUMN cc_qc_third_record_info.ID IS 'ID';
COMMENT ON COLUMN cc_qc_third_record_info.SERIAL_ID IS '序列号';
COMMENT ON COLUMN cc_qc_third_record_info.MSG_TIME IS '消息时间';
COMMENT ON COLUMN cc_qc_third_record_info.CHANNEL IS '消息所属角色,0表示客户，1表示坐席';
COMMENT ON COLUMN cc_qc_third_record_info.MSG_TYPE IS '消息类型';
COMMENT ON COLUMN cc_qc_third_record_info.MSG_CONTENT IS '消息内容';
COMMENT ON COLUMN cc_qc_third_record_info.CREATE_TIME IS '创建时间';
---- 创建索引
CREATE INDEX idx_serial_id ON cc_qc_third_record_info (SERIAL_ID);
CREATE INDEX idx_msg_time ON cc_qc_third_record_info (MSG_TIME);

-- 20241126 获取任务的坐席质检数量统计sql优化
CREATE INDEX idx_task_agent ON cc_qc_task_obj (TASK_ID, AGENT_ID);

-- 20241127 CC_QC_THIRD_RECORD增加基本索引
create index  IDX_CC_QC_THIRD_RECORD1 ON CC_QC_THIRD_RECORD(SERIAL_ID);
create index  IDX_CC_QC_THIRD_RECORD2 ON CC_QC_THIRD_RECORD(CREATE_TIME);

-- 20241204 质检看板-慢sql优化
CREATE INDEX IDX_CC_QC_TASK_OBJ17 ON CC_QC_TASK_OBJ (RG_STATE, RG_QC_SCORE);

---- 添加ITEM_TYPE列到CC_QC_DETAILS表，并设置数据类型为VARCHAR(30)，添加注释
ALTER TABLE "CC_QC_DETAILS" ADD COLUMN "ITEM_TYPE" VARCHAR(30);
COMMENT ON COLUMN "CC_QC_DETAILS"."ITEM_TYPE" IS '质检项类型';

---- 添加NODE_TYPE列到CC_QC_DETAILS表，并设置数据类型为VARCHAR(30)，添加注释
---- ALTER TABLE CC_QC_DETAILS ALTER COLUMN NODE_TYPE SET DEFAULT '1'; update CC_QC_DETAILS set NODE_TYPE='1';
ALTER TABLE "CC_QC_DETAILS" ADD COLUMN "NODE_TYPE" VARCHAR(30) DEFAULT '1';
COMMENT ON COLUMN "CC_QC_DETAILS"."NODE_TYPE" IS '1：普通节点 2：逻辑判断节点|进行分隔';

---- 添加P_ITEM_ID列到CC_QC_DETAILS表，并设置数据类型为VARCHAR(30)，添加注释
ALTER TABLE "CC_QC_DETAILS" ADD COLUMN "P_ITEM_ID" VARCHAR(30);
COMMENT ON COLUMN "CC_QC_DETAILS"."P_ITEM_ID" IS '质检项父ID';

---- 添加HIGHLIGHT_SENTENCE列到CC_QC_DETAILS表，并设置数据类型为VARCHAR(2000)，添加注释
ALTER TABLE "CC_QC_DETAILS" ADD COLUMN "HIGHLIGHT_SENTENCE" VARCHAR(2000);
COMMENT ON COLUMN "CC_QC_DETAILS"."HIGHLIGHT_SENTENCE" IS '多个关键词或话术使用|进行分隔';

-- 20241217
ALTER TABLE CC_QC_TASK_OBJ ALTER COLUMN AGENT_DEPT_CODE TYPE VARCHAR(64);
COMMENT ON COLUMN CC_QC_TASK_OBJ.AGENT_DEPT_CODE IS '坐席所在部门编号';

-- 20241220 第三方记录表添加索引
CREATE INDEX idx_serialId_templateId ON cc_qc_third_record (SERIAL_ID, TEMPLATE_ID);
CREATE INDEX idx_serial_id ON cc_qc_third_record (SERIAL_ID);
CREATE INDEX idx_create_time ON cc_qc_third_record (CREATE_TIME);
CREATE INDEX idx_begin_end_time ON cc_qc_third_record (BEGIN_TIME, END_TIME);
CREATE INDEX idx_ent_id_busi_order_id ON cc_qc_third_record (ENT_ID, BUSI_ORDER_ID);
CREATE INDEX idx_agent_id ON cc_qc_third_record (AGENT_ID);

-- 20250224
ALTER TABLE "cc_qc_details" ADD COLUMN "create_time" VARCHAR(30);
COMMENT ON COLUMN "cc_qc_details"."create_time" IS '创建时间';

ALTER TABLE "cc_qc_transfer" ADD COLUMN "create_time" VARCHAR(30);
COMMENT ON COLUMN "cc_qc_transfer"."create_time" IS '创建时间';

-- 20250227
ALTER TABLE "cc_qc_details" ADD COLUMN "hit_count_infos" VARCHAR(2000);
COMMENT ON COLUMN "cc_qc_details"."hit_count_infos" IS '质检项命中次数JSON';



-- 20250312 待新增字段
ALTER TABLE "cc_qc_capacity" ADD COLUMN "zn_evaluate_result" VARCHAR(2000);
COMMENT ON COLUMN "cc_qc_capacity"."zn_evaluate_result" IS '智能质检扣分项结果';

-- 20250317 辅导字段扩容
ALTER TABLE cc_qc_result ALTER COLUMN cach_exjosn TYPE VARCHAR(3000);
ALTER TABLE cc_qc_result ALTER COLUMN cach_bakup TYPE VARCHAR(3000);

-- 20250317 新增asr录音属性记录表
CREATE TABLE cc_qc_asr_info
(
    id                   VARCHAR(64) NOT NULL PRIMARY KEY,
    serial_id            VARCHAR(64),
    call_duration        INT,
    agent_silence_time   INT,
    user_silence_time    INT,
    agent_silence_num    INT,
    user_silence_num     INT,
    agent_rob_num        INT,
    user_rob_num         INT,
    agent_rob_time       INT,
    user_rob_time        INT,
    agent_average_volume INT,
    user_average_volume  INT,
    overall_silence      INT,
    silence_ratio        DECIMAL(10, 2),
    agent_average_speed  DECIMAL(10, 2),
    user_average_speed   DECIMAL(10, 2),
    create_time          VARCHAR(32)
);
COMMENT ON TABLE cc_qc_asr_info IS 'asr录音属性记录表';
COMMENT ON COLUMN cc_qc_asr_info.id IS '主键ID';
COMMENT ON COLUMN cc_qc_asr_info.serial_id IS '唯一标识字段';
COMMENT ON COLUMN cc_qc_asr_info.call_duration IS '通话时长（单位/秒）';
COMMENT ON COLUMN cc_qc_asr_info.agent_silence_time IS '座席静音总时长（单位/秒）';
COMMENT ON COLUMN cc_qc_asr_info.user_silence_time IS '用户静音总时长（单位/秒）';
COMMENT ON COLUMN cc_qc_asr_info.agent_silence_num IS '座席静音次数';
COMMENT ON COLUMN cc_qc_asr_info.user_silence_num IS '用户静音次数';
COMMENT ON COLUMN cc_qc_asr_info.agent_rob_num IS '座席抢话次数';
COMMENT ON COLUMN cc_qc_asr_info.user_rob_num IS '用户抢话次数';
COMMENT ON COLUMN cc_qc_asr_info.agent_rob_time IS '座席抢话时长（单位/秒）';
COMMENT ON COLUMN cc_qc_asr_info.user_rob_time IS '用户抢话时长（单位/秒）';
COMMENT ON COLUMN cc_qc_asr_info.agent_average_volume IS '坐席平均音量';
COMMENT ON COLUMN cc_qc_asr_info.user_average_volume IS '用户平均音量';
COMMENT ON COLUMN cc_qc_asr_info.overall_silence IS '静音';
COMMENT ON COLUMN cc_qc_asr_info.silence_ratio IS '坐席静音比';
COMMENT ON COLUMN cc_qc_asr_info.agent_average_speed IS '座席平均语速';
COMMENT ON COLUMN cc_qc_asr_info.user_average_speed IS '用户平均语速';
COMMENT ON COLUMN cc_qc_asr_info.create_time IS '创建时间';
CREATE INDEX idx_cc_qc_asr_info_1 ON cc_qc_asr_info (serial_id);

-- 20250328 字段扩容
ALTER TABLE cc_qc_result_item ALTER COLUMN mark_value TYPE VARCHAR(500);
ALTER TABLE cc_qc_result_item_his ALTER COLUMN mark_value TYPE VARCHAR(500);
ALTER TABLE cc_qc_task_obj ALTER COLUMN ex_json TYPE VARCHAR(2000);

-- 20250402 智能质检结果高亮词扩容
ALTER TABLE cc_qc_details ALTER COLUMN highlight_word TYPE VARCHAR(2000);

-- 20250419 智能质检结果详情记录表新增 智能质检标签字段
ALTER TABLE cc_qc_details ADD COLUMN tag_list VARCHAR(2000);
COMMENT ON COLUMN cc_qc_details.tag_list IS '智能质检标签字段';

-- 20250422 转写记录表内容字段扩容
ALTER TABLE cc_qc_transfer ALTER COLUMN content TYPE VARCHAR(2000);

-- 20250609 质检结果记录表补充 score_desc 字段
ALTER TABLE "cc_qc_result_item" ADD COLUMN "score_desc" VARCHAR(255);

-- 20250617 创建 cc_qc_review_log 表
CREATE TABLE cc_qc_review_log (
                                  ID             VARCHAR(64) PRIMARY KEY,
                                  CAPACITY_ID    VARCHAR(64),
                                  SERIAL_ID      VARCHAR(64),
                                  REVIEWER_ID    VARCHAR(50),
                                  SCORE          NUMERIC(10,2),
                                  IS_VOTE        VARCHAR(2),
                                  REMARK         VARCHAR(255),
                                  VERSION        INT,
                                  CREATE_TIME    VARCHAR(19)
);

-- 添加列注释
COMMENT ON COLUMN cc_qc_review_log.ID IS '主键';
COMMENT ON COLUMN cc_qc_review_log.CAPACITY_ID IS '对应 cc_qc_capacity.capacityId';
COMMENT ON COLUMN cc_qc_review_log.SERIAL_ID IS '话单ID';
COMMENT ON COLUMN cc_qc_review_log.REVIEWER_ID IS '复检人';
COMMENT ON COLUMN cc_qc_review_log.SCORE IS '本次复检的分数';
COMMENT ON COLUMN cc_qc_review_log.IS_VOTE IS '是否一票否决';
COMMENT ON COLUMN cc_qc_review_log.REMARK IS '复检评语';
COMMENT ON COLUMN cc_qc_review_log.VERSION IS '第几次复检（从1开始）';
COMMENT ON COLUMN cc_qc_review_log.CREATE_TIME IS '创建时间';

-- 添加表注释
COMMENT ON TABLE cc_qc_review_log IS '复检记录日志表';

-- 在 cc_qc_capacity 表中新增字段
ALTER TABLE cc_qc_capacity
    ADD COLUMN REVIEW_SCORE NUMERIC(10,2);

-- 添加列注释（假设注释也想保留）
COMMENT ON COLUMN cc_qc_capacity.REVIEW_SCORE IS '复检分数';

-- 20250626 cc_qc_task_obj 增加 暂存标识  1为暂存 并增加数据更新sql
ALTER TABLE cc_qc_task_obj ADD COLUMN IS_TEMP_QC VARCHAR(20);
COMMENT ON COLUMN cc_qc_task_obj.IS_TEMP_QC IS '是否暂存质检';

UPDATE cc_qc_task_obj SET IS_TEMP_QC = '1' WHERE RG_STATE = '2' AND RG_QC_SCORE IS NOT NULL AND IS_TEMP_QC IS NULL;

-- 20250702
-- 质检测试任务记录表
CREATE TABLE cc_qc_test_task
(
    ID            VARCHAR(64) NOT NULL,
    TASK_NAME     VARCHAR(64) NOT NULL,
    MODEL_ID      VARCHAR(64),
    MODEL_NAME    VARCHAR(100),
    BUSI_TYPE     VARCHAR(10),
    STATE         VARCHAR(10),
    ZN_QC_TYPE    INT,
    RECORD_COUNT  INT DEFAULT 0,
    QC_COUNT      INT DEFAULT 0,
    ENT_ID        VARCHAR(32),
    BUSI_ORDER_ID VARCHAR(64),
    CREATE_ACC    VARCHAR(32),
    CREATE_TIME   VARCHAR(20),
    UPDATE_ACC    VARCHAR(32),
    UPDATE_TIME   VARCHAR(20),
    DATE_ID       INT DEFAULT 0,
    MONTH_ID      INT DEFAULT 0,
    BAKUP         VARCHAR(500),
    EX_JSON       VARCHAR(1000),
    PRIMARY KEY (ID)
);
CREATE INDEX idx_test_task_create_time ON cc_qc_test_task (CREATE_TIME);
COMMENT ON TABLE cc_qc_test_task IS '质检测试任务记录表';
COMMENT ON COLUMN cc_qc_test_task.ID IS '主键ID';
COMMENT ON COLUMN cc_qc_test_task.TASK_NAME IS '任务名称';
COMMENT ON COLUMN cc_qc_test_task.MODEL_ID IS '模型ID';
COMMENT ON COLUMN cc_qc_test_task.MODEL_NAME IS '模型名称';
COMMENT ON COLUMN cc_qc_test_task.BUSI_TYPE IS '业务类型: 1-语音 2-文本';
COMMENT ON COLUMN cc_qc_test_task.STATE IS '状态: 1-启动 0-关闭';
COMMENT ON COLUMN cc_qc_test_task.ZN_QC_TYPE IS  '智能质检类型: 1:大模型质检 2:AI质检';
COMMENT ON COLUMN cc_qc_test_task.RECORD_COUNT IS '记录总数';
COMMENT ON COLUMN cc_qc_test_task.QC_COUNT IS '质检总数';
COMMENT ON COLUMN cc_qc_test_task.ENT_ID IS '企业ID';
COMMENT ON COLUMN cc_qc_test_task.BUSI_ORDER_ID IS '业务ID';
COMMENT ON COLUMN cc_qc_test_task.CREATE_ACC IS '创建人';
COMMENT ON COLUMN cc_qc_test_task.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN cc_qc_test_task.UPDATE_ACC IS '更新人';
COMMENT ON COLUMN cc_qc_test_task.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN cc_qc_test_task.DATE_ID IS '创建日期ID，如20250508';
COMMENT ON COLUMN cc_qc_test_task.MONTH_ID IS '创建月份，如2025';
COMMENT ON COLUMN cc_qc_test_task.BAKUP IS '备注';
COMMENT ON COLUMN cc_qc_test_task.EX_JSON IS '扩展参数，存储json';

-- 质检测试对象记录表
CREATE TABLE cc_qc_test_task_obj
(
    ID             VARCHAR(64) NOT NULL,
    TASK_ID        VARCHAR(64) NOT NULL,
    ATTACH_ID      VARCHAR(64),
    OBJ_NAME       VARCHAR(200),
    OBJ_LENGTH     INT         DEFAULT 0,
    OBJ_PATH       VARCHAR(200),
    OBJ_SUFFIX     VARCHAR(20),
    MODEL_ID       VARCHAR(64),
    MODEL_NAME     VARCHAR(100),
    TRANSFER_STATE VARCHAR(10) DEFAULT '1',
    QC_STATE       VARCHAR(10) DEFAULT '1',
    FAIL_COUNT     INT         DEFAULT 0,
    BUSI_TYPE      VARCHAR(10),
    QC_TIME        VARCHAR(20),
    BASE_SCORE     VARCHAR(10),
    SCORE          NUMERIC(10, 2),
    PASS_FLAG      VARCHAR(10),
    RESULT_ID      VARCHAR(64),
    IS_VETO        VARCHAR(20),
    IS_REVIEW      INT         DEFAULT 0,
    REVIEW_SCORE   NUMERIC(10, 2),
    ENT_ID         VARCHAR(32),
    BUSI_ORDER_ID  VARCHAR(64),
    CREATE_ACC     VARCHAR(32),
    CREATE_TIME    VARCHAR(20),
    UPDATE_ACC     VARCHAR(32),
    UPDATE_TIME    VARCHAR(20),
    DATE_ID        INT         DEFAULT 0,
    MONTH_ID       INT         DEFAULT 0,
    BAKUP          VARCHAR(500),
    EX_JSON        TEXT,
    PRIMARY KEY (ID)
);
CREATE INDEX idx_qc_time ON cc_qc_test_task_obj (QC_TIME);
CREATE INDEX idx_score ON cc_qc_test_task_obj (SCORE);
COMMENT ON TABLE cc_qc_test_task_obj IS '质检测试对象记录表';
COMMENT ON COLUMN cc_qc_test_task_obj.ID IS '主键ID';
COMMENT ON COLUMN cc_qc_test_task_obj.TASK_ID IS '所属任务ID';
COMMENT ON COLUMN cc_qc_test_task_obj.ATTACH_ID IS '附件ID';
COMMENT ON COLUMN cc_qc_test_task_obj.OBJ_NAME IS '文件名称';
COMMENT ON COLUMN cc_qc_test_task_obj.OBJ_LENGTH IS '文件大小，字节';
COMMENT ON COLUMN cc_qc_test_task_obj.OBJ_PATH IS '文件路径,上传到本地后的路径';
COMMENT ON COLUMN cc_qc_test_task_obj.OBJ_SUFFIX IS '文件格式(后缀)，如mp3、wav、docs等等';
COMMENT ON COLUMN cc_qc_test_task_obj.MODEL_ID IS '模型ID 冗余字段';
COMMENT ON COLUMN cc_qc_test_task_obj.MODEL_NAME IS '模型名称 冗余字段';
COMMENT ON COLUMN cc_qc_test_task_obj.TRANSFER_STATE IS '转写状态  1-未转写 2-转写中 3-转写成功 4-转写失败 9-无需转写';
COMMENT ON COLUMN cc_qc_test_task_obj.QC_STATE IS '质检状态 1-待质检  2-质检中 3-质检成功  4-质检失败';
COMMENT ON COLUMN cc_qc_test_task_obj.FAIL_COUNT IS '质检失败次数';
COMMENT ON COLUMN cc_qc_test_task_obj.BUSI_TYPE IS '业务类型: 1-语音 2-文本';
COMMENT ON COLUMN cc_qc_test_task_obj.QC_TIME IS '质检时间';
COMMENT ON COLUMN cc_qc_test_task_obj.BASE_SCORE IS '模型基础分数';
COMMENT ON COLUMN cc_qc_test_task_obj.SCORE IS '质检分数';
COMMENT ON COLUMN cc_qc_test_task_obj.PASS_FLAG IS '质检结果标识: 0-通过 1-不通过';
COMMENT ON COLUMN cc_qc_test_task_obj.RESULT_ID IS '质检结果ID（质检平台的唯一标识）';
COMMENT ON COLUMN cc_qc_test_task_obj.IS_VETO IS '是否一票否决，0-否 1-是';
COMMENT ON COLUMN cc_qc_test_task_obj.IS_REVIEW IS '是否复检，0-否 1-是 拓展字段暂无使用';
COMMENT ON COLUMN cc_qc_test_task_obj.REVIEW_SCORE IS '复检分数 拓展字段暂无使用';
COMMENT ON COLUMN cc_qc_test_task_obj.ENT_ID IS '企业ID';
COMMENT ON COLUMN cc_qc_test_task_obj.BUSI_ORDER_ID IS '业务ID';
COMMENT ON COLUMN cc_qc_test_task_obj.CREATE_ACC IS '创建人';
COMMENT ON COLUMN cc_qc_test_task_obj.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN cc_qc_test_task_obj.UPDATE_ACC IS '更新人';
COMMENT ON COLUMN cc_qc_test_task_obj.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN cc_qc_test_task_obj.DATE_ID IS '创建日期ID，如20250508';
COMMENT ON COLUMN cc_qc_test_task_obj.MONTH_ID IS '创建月份，如2025';
COMMENT ON COLUMN cc_qc_test_task_obj.BAKUP IS '备注';
COMMENT ON COLUMN cc_qc_test_task_obj.EX_JSON IS '扩展参数，存储json';

-- 测试记录智能质检得分详情表
CREATE TABLE cc_qc_test_details
(
    ID                 VARCHAR(64) NOT NULL,
    OBJ_ID             VARCHAR(64),
    QUANAME_ID         VARCHAR(64),
    QUA_NAME           VARCHAR(200),
    ITEMNAME_ID        VARCHAR(64),
    ITEM_NAME          VARCHAR(200),
    IS_CAUSE           VARCHAR(200),
    REMARK             VARCHAR(400),
    START_TIME         VARCHAR(19),
    HIGHLIGHT_WORD     VARCHAR(2000),
    SCORE              NUMERIC(10, 2),
    ZN_ID              VARCHAR(64),
    IS_VOTE            INT,
    ITEM_TYPE          VARCHAR(30),
    NODE_TYPE          VARCHAR(30),
    P_ITEM_ID          VARCHAR(30),
    HIGHLIGHT_SENTENCE VARCHAR(1000),
    CREATE_TIME        VARCHAR(30),
    HIT_COUNT_INFOS    VARCHAR(2000),
    TAG_LIST           VARCHAR(2000),
    PRIMARY KEY (ID)
);
CREATE INDEX IDX_CC_QC_TEST_DETAILS_1 ON cc_qc_test_details (OBJ_ID, ITEM_NAME);
COMMENT ON TABLE cc_qc_test_details IS '测试记录智能质检得分详情表';
COMMENT ON COLUMN cc_qc_test_details.ID IS 'ID';
COMMENT ON COLUMN cc_qc_test_details.OBJ_ID IS '质检对象ID';
COMMENT ON COLUMN cc_qc_test_details.QUANAME_ID IS '质检标题ID,存在智能质检系统中';
COMMENT ON COLUMN cc_qc_test_details.QUA_NAME IS '质检标题,存在智能质检系统中';
COMMENT ON COLUMN cc_qc_test_details.ITEMNAME_ID IS '质检项ID,存在智能质检系统中';
COMMENT ON COLUMN cc_qc_test_details.ITEM_NAME IS '质检项,存在智能质检系统中';
COMMENT ON COLUMN cc_qc_test_details.IS_CAUSE IS '加扣分原因,用 | 分隔';
COMMENT ON COLUMN cc_qc_test_details.REMARK IS '备注';
COMMENT ON COLUMN cc_qc_test_details.START_TIME IS '开始时间';
COMMENT ON COLUMN cc_qc_test_details.HIGHLIGHT_WORD IS '高亮词';
COMMENT ON COLUMN cc_qc_test_details.SCORE IS '得分';
COMMENT ON COLUMN cc_qc_test_details.ZN_ID IS '智能质检-质检项明细ID';
COMMENT ON COLUMN cc_qc_test_details.IS_VOTE IS '是否一票否决，0-否 1-是';
COMMENT ON COLUMN cc_qc_test_details.ITEM_TYPE IS '质检项类型  0：普通质检项 1：流程质检项';
COMMENT ON COLUMN cc_qc_test_details.NODE_TYPE IS '固定为2 1：普通节点 2：逻辑判断节点|进行分隔';
COMMENT ON COLUMN cc_qc_test_details.P_ITEM_ID IS '质检项父ID';
COMMENT ON COLUMN cc_qc_test_details.HIGHLIGHT_SENTENCE IS '多个关键词或话术使用|进行分隔';
COMMENT ON COLUMN cc_qc_test_details.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN cc_qc_test_details.HIT_COUNT_INFOS IS '质检项命中次数JSON';
COMMENT ON COLUMN cc_qc_test_details.TAG_LIST IS '智能质检标签结果';


-- 测试记录通话录音转写记录详情表
CREATE TABLE cc_qc_test_transfer
(
    ID           VARCHAR(64) NOT NULL,
    OBJ_ID       VARCHAR(64),
    START_TIME   VARCHAR(39),
    END_TIME     VARCHAR(39),
    TYPE         VARCHAR(10),
    CONTENT      VARCHAR(2000),
    START_OFFSET VARCHAR(30),
    END_OFFSET   VARCHAR(30),
    CREATE_TIME  VARCHAR(30),
    PRIMARY KEY (ID)
);
CREATE INDEX IDX_CC_QC_TEST_TRANSFER_1 ON cc_qc_test_transfer (OBJ_ID);
COMMENT ON TABLE cc_qc_test_transfer IS '测试记录通话录音转写记录详情表';
COMMENT ON COLUMN cc_qc_test_transfer.ID IS 'ID';
COMMENT ON COLUMN cc_qc_test_transfer.OBJ_ID IS '质检对象ID';
COMMENT ON COLUMN cc_qc_test_transfer.START_TIME IS '每句话后的开始时间';
COMMENT ON COLUMN cc_qc_test_transfer.END_TIME IS '每句话后的结束时间';
COMMENT ON COLUMN cc_qc_test_transfer.TYPE IS '客户还是客服，0-客户 1-客服';
COMMENT ON COLUMN cc_qc_test_transfer.CONTENT IS '转写内容';
COMMENT ON COLUMN cc_qc_test_transfer.START_OFFSET IS '开始偏移量';
COMMENT ON COLUMN cc_qc_test_transfer.END_OFFSET IS '结束偏移量';
COMMENT ON COLUMN cc_qc_test_transfer.CREATE_TIME IS '创建时间';

-- 测试记录ASR录音属性表
CREATE TABLE cc_qc_test_asr_info
(
    ID                   VARCHAR(64) NOT NULL,
    OBJ_ID               VARCHAR(64),
    CALL_DURATION        INT,
    AGENT_SILENCE_TIME   INT,
    USER_SILENCE_TIME    INT,
    AGENT_SILENCE_NUM    INT,
    USER_SILENCE_NUM     INT,
    AGENT_ROB_NUM        INT,
    USER_ROB_NUM         INT,
    AGENT_ROB_TIME       INT,
    USER_ROB_TIME        INT,
    AGENT_AVERAGE_VOLUME INT,
    USER_AVERAGE_VOLUME  INT,
    OVERALL_SILENCE      INT,
    SILENCE_RATIO        NUMERIC(10, 2),
    AGENT_AVERAGE_SPEED  NUMERIC(10, 2),
    USER_AVERAGE_SPEED   NUMERIC(10, 2),
    CREATE_TIME          VARCHAR(32),
    PRIMARY KEY (ID)
);
CREATE INDEX idx_OBJ_ID ON cc_qc_test_asr_info (OBJ_ID);
COMMENT ON TABLE cc_qc_test_asr_info IS '测试记录ASR录音属性表';
COMMENT ON COLUMN cc_qc_test_asr_info.ID IS '主键ID';
COMMENT ON COLUMN cc_qc_test_asr_info.OBJ_ID IS '质检对象ID';
COMMENT ON COLUMN cc_qc_test_asr_info.CALL_DURATION IS '通话时长（单位/秒）';
COMMENT ON COLUMN cc_qc_test_asr_info.AGENT_SILENCE_TIME IS '座席静音总时长（单位/秒）';
COMMENT ON COLUMN cc_qc_test_asr_info.USER_SILENCE_TIME IS '用户静音总时长（单位/秒）';
COMMENT ON COLUMN cc_qc_test_asr_info.AGENT_SILENCE_NUM IS '座席静音次数';
COMMENT ON COLUMN cc_qc_test_asr_info.USER_SILENCE_NUM IS '用户静音次数';
COMMENT ON COLUMN cc_qc_test_asr_info.AGENT_ROB_NUM IS '座席抢话次数';
COMMENT ON COLUMN cc_qc_test_asr_info.USER_ROB_NUM IS '用户抢话次数';
COMMENT ON COLUMN cc_qc_test_asr_info.AGENT_ROB_TIME IS '座席抢话时长（单位/秒）';
COMMENT ON COLUMN cc_qc_test_asr_info.USER_ROB_TIME IS '用户抢话时长（单位/秒）';
COMMENT ON COLUMN cc_qc_test_asr_info.AGENT_AVERAGE_VOLUME IS '坐席平均音量';
COMMENT ON COLUMN cc_qc_test_asr_info.USER_AVERAGE_VOLUME IS '用户平均音量';
COMMENT ON COLUMN cc_qc_test_asr_info.OVERALL_SILENCE IS '静音';
COMMENT ON COLUMN cc_qc_test_asr_info.SILENCE_RATIO IS '坐席静音比';
COMMENT ON COLUMN cc_qc_test_asr_info.AGENT_AVERAGE_SPEED IS '座席平均语速';
COMMENT ON COLUMN cc_qc_test_asr_info.USER_AVERAGE_SPEED IS '用户平均语速';
COMMENT ON COLUMN cc_qc_test_asr_info.CREATE_TIME IS '创建时间';

CREATE TABLE "cc_qc_test_llm_result"
(
    "id"              varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
    "obj_id"          varchar(64) COLLATE "pg_catalog"."default",
    "msg_id"          varchar(64) COLLATE "pg_catalog"."default",
    "chat_session_id" varchar(64) COLLATE "pg_catalog"."default",
    "session_id"      varchar(64) COLLATE "pg_catalog"."default",
    "zn_class_id"     varchar(64) COLLATE "pg_catalog"."default",
    "question"        text COLLATE "pg_catalog"."default",
    "reason_content"  text COLLATE "pg_catalog"."default",
    "content"         text COLLATE "pg_catalog"."default",
    "total_token"     int8,
    "start_time"      int8,
    "end_time"        int8,
    "is_review"       int4 DEFAULT 0,
    "ent_id"          varchar(30) COLLATE "pg_catalog"."default",
    "busi_order_id"   varchar(64) COLLATE "pg_catalog"."default",
    "creat_time"      varchar(30) COLLATE "pg_catalog"."default",
    primary key (id)
);
COMMENT ON COLUMN "cc_qc_test_llm_result"."id" IS '主键';
COMMENT ON COLUMN "cc_qc_test_llm_result"."obj_id" IS '质检对象记录ID';
COMMENT ON COLUMN "cc_qc_test_llm_result"."msg_id" IS '请求的消息id';
COMMENT ON COLUMN "cc_qc_test_llm_result"."chat_session_id" IS '请求的会话id，同一次会话，必须唯一';
COMMENT ON COLUMN "cc_qc_test_llm_result"."session_id" IS '请求者标识，可以是客户id、坐席账号或自定义';
COMMENT ON COLUMN "cc_qc_test_llm_result"."zn_class_id" IS '模型质检规则ID';
COMMENT ON COLUMN "cc_qc_test_llm_result"."question" IS '咨询的问题';
COMMENT ON COLUMN "cc_qc_test_llm_result"."reason_content" IS '思考内容';
COMMENT ON COLUMN "cc_qc_test_llm_result"."content" IS '响应内容,即质检的结果';
COMMENT ON COLUMN "cc_qc_test_llm_result"."total_token" IS '耗费token数量';
COMMENT ON COLUMN "cc_qc_test_llm_result"."start_time" IS '消息处理开始时间戳';
COMMENT ON COLUMN "cc_qc_test_llm_result"."end_time" IS '消息处理结束时间戳';
COMMENT ON COLUMN "cc_qc_test_llm_result"."is_review" IS '是否复检，0-否 1-是 默认为0';
COMMENT ON COLUMN "cc_qc_test_llm_result"."ent_id" IS '企业ID';
COMMENT ON COLUMN "cc_qc_test_llm_result"."busi_order_id" IS '订购Id';
COMMENT ON COLUMN "cc_qc_test_llm_result"."creat_time" IS '创建时间';

-- 20250724  慢sql优化
CREATE INDEX idx_cc_qc_task_obj_date_ent_busiorder
    ON cc_qc_task_obj (date_id, ent_id, busi_order_id);
CREATE INDEX idx_cc_qc_third_record_ex19_ex4
    ON cc_qc_third_record (ex_19, ex_4);

-- 20250725  CC_QC_ZN_ITEM_STAT新增字段
ALTER TABLE CC_QC_ZN_ITEM_STAT ADD COLUMN TEMPLATE_ID VARCHAR(64);

ALTER TABLE CC_QC_ZN_ITEM_STAT ADD COLUMN GROUP_NAME VARCHAR(30);

ALTER TABLE CC_QC_ZN_ITEM_STAT ADD COLUMN AGENT_NAME VARCHAR(30);

-- 20250806  创建序列用于生成CC_QC_ZN_ITEM_STAT表的主键
CREATE SEQUENCE cc_qc_zn_item_stat_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1000;

-- 20250811  删除重复的 serial_id
-- 查看重复的 serial_id 及其数量
SELECT serial_id, COUNT(*)
FROM cc_qc_third_record
GROUP BY serial_id
HAVING COUNT(*) > 1;

-- 仅保留每组第一条（rn=1），删除其余（rn>1）
DELETE FROM cc_qc_third_record
WHERE id IN (
    SELECT id
    FROM (
             SELECT
                 id,
                 ROW_NUMBER() OVER (PARTITION BY serial_id ORDER BY id) AS rn  -- 按 serial_id 分组，id 升序排序（保留最小 id）
             FROM cc_qc_third_record
         ) t
    WHERE rn > 1
);

-- 20250813  修改CC_QC_RESULT_RECONSIDER 表的 DESCRIPTION1 和 DESCRIPTION2 字段大小为 2000
ALTER TABLE cc_qc_result_reconsider ALTER COLUMN description1 TYPE VARCHAR(2000);
COMMENT ON COLUMN cc_qc_result_reconsider.description1 IS '第一次复议描述';
ALTER TABLE cc_qc_result_reconsider ALTER COLUMN description2 TYPE VARCHAR(2000);
COMMENT ON COLUMN cc_qc_result_reconsider.description2 IS '第二次复议描述';

-- 20250814  修改CC_QC_RESULT表的 SERIAL_ID  字段大小为64
ALTER TABLE cc_qc_result ALTER COLUMN serial_id TYPE VARCHAR(64);

-- 20250819  字段扩容
ALTER TABLE cc_qc_result_item ALTER COLUMN item_name TYPE VARCHAR(500);
ALTER TABLE cc_qc_result_item_his ALTER COLUMN item_name TYPE VARCHAR(500);

-- 20250821 质检工作台优化
ALTER TABLE CC_QC_CAPACITY ADD COLUMN "quality_evaluate" VARCHAR(2000);
COMMENT ON COLUMN CC_QC_CAPACITY.quality_evaluate IS 'AI质检结果评价';
ALTER TABLE CC_QC_DETAILS ADD COLUMN "item_evaluate" VARCHAR(2000);
COMMENT ON COLUMN CC_QC_DETAILS.item_evaluate IS 'AI质检项评价';
ALTER TABLE CC_QC_TEST_DETAILS ADD COLUMN "item_evaluate" VARCHAR(2000);
COMMENT ON COLUMN CC_QC_TEST_DETAILS.item_evaluate IS 'AI质检结果评价';
ALTER TABLE CC_QC_TEST_TASK_OBJ ADD COLUMN "quality_evaluate" VARCHAR(2000);
COMMENT ON COLUMN CC_QC_TEST_TASK_OBJ.quality_evaluate IS 'AI质检项评价';

-- 20250826  定时任务新增字段
ALTER TABLE ycmain.C_CF_JOB  ADD COLUMN MODULE varchar(20) DEFAULT '99';
COMMENT ON COLUMN ycmain.C_CF_JOB.MODULE IS '所属模块   SYSTEM_MODULE  01-在线客服...99-无';