<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>第三方质检模板</title>
	<link rel="stylesheet" href="/cc-base/static/lib/element/element-ui.css">
	<link rel="stylesheet" href="/easitline-static/lib/bootstrap/css/bootstrap.min.css">
	<style>
		.table-wrapper {
			height: calc(100% - 250px);
		}
		html ,body, #fields{
			height: calc(100% - 10px);
		}
		.el-tabs{
			height: 100%;
		}

		.el-tabs__nav.is-left{
			margin-top:40px;
		}

		.layui-layer-title {
			 border-bottom: 0 !important;
			 background-color: white !important;
			/* border-radius: 2px 2px 0 0; */
		}
 .el-select {
  width: 100%;
}
	</style>
</head>
<body>
<div id="fields">
  <el-container>
				<el-main>
					<el-form ref="form" label-width="90px" :model="form" :rules="rules" size="medium">
						<el-input  v-show="false" v-model="form.ID" ></el-input>						
						<el-divider content-position="left"></el-divider>
						<el-row :gutter="20">
						  <el-col :xl="8" :lg="12" :md="16" :sm="24" :xs="48">
								<el-form-item :label="getI18nValue('模板名称')" prop="TEMPLATE_NAME">
									<el-input maxlength="15" v-model="form.TEMPLATE_NAME" ></el-input>
								</el-form-item>
							</el-col>
						  <el-col :xl="8" :lg="12" :md="16" :sm="24" :xs="48">
								
								<el-form-item :label="getI18nValue('业务类型')" prop="BUSI_TYPE">
									<el-select v-model="form.BUSI_TYPE" placeholder="请选择">
										<el-option v-for="item in busiTypeList" :key="item.value" :label="item.label" :value="item.value">
									</el-option>
								</el-select>								
								</el-form-item>
							</el-col>
						</el-row>	
						<el-row :gutter="20">
						  <el-col :xl="8" :lg="12" :md="16" :sm="24" :xs="48">
								<el-form-item :label="getI18nValue('需要回调')">
									<el-select v-model="form.CALLBACK" placeholder="请选择">
										<el-option v-for="item in callBackList" :key="item.value" :label="item.label" :value="item.value">
									</el-option>
									
								</el-form-item>
							</el-col>
						  <el-col :xl="8" :lg="12" :md="16" :sm="24" :xs="48">
								<el-form-item :label="getI18nValue('回调地址')">
									<el-input v-model="form.CALLBACK_URL" ></el-input>
									
								</el-form-item>
							</el-col>
						</el-row>

<!--						todo 新增asr转写地址配置 以及 并发数配置-->

						<el-row :gutter="20">
						  <el-col :xl="8" :lg="12" :md="16" :sm="24" :xs="48">
								<el-form-item :label="getI18nValue('描述')" prop="orderNo">
									<el-input  maxlength="50" type="textarea" :rows="2" v-model="form.TEMPLATE_DESC" ></el-input>
								</el-form-item>
							</el-col>
							
						</el-row>
						
						<el-row :gutter="20"  style="text-align: center;margin-top: 20px;">						
						  <el-col :xl="16" :lg="24" :md="32" :sm="48" :xs="96">
								<el-button type="primary" id="save" size="medium" @click="submit('form')">{{getI18nValue("保存")}}</el-button>							
							</el-col>						  
						</el-row>

 					
					</el-form>
				</el-main>
			</el-container>

		
	
	<div style="margin-bottom: 20px;" ref="tableWrapper" class="table-wrapper">
		<template>
			<el-table :data="fields" ref="table" style="width: 100%" max-height="700">
				<el-table-column
						type="index"
						width="50">
				</el-table-column>		
					
				<el-table-column :label="getI18nValue('字段名')" min-width="180" align="center">
					<template slot-scope="scope">
						<el-input v-model="scope.row.id" v-show="false"></el-input>	
						<el-input v-model="scope.row.buildIn" v-show="false"></el-input>					
						<el-input v-model="scope.row.showType" v-show="false"></el-input>															
						<el-input v-model="scope.row.fieldEn" disabled="true"></el-input>
					</template>
				</el-table-column>
				<el-table-column :label="getI18nValue('显示列名')" min-width="180" align="center">
					<template slot-scope="scope">
						<el-input maxlength="15" v-model="scope.row.fieldCn" :disabled="scope.row.buildIn == 'Y' ? true : false"></el-input>
					</template>
				</el-table-column>
				<el-table-column :label="getI18nValue('显示配置')" min-width="100" align="center">
				
					<template slot-scope="scope">
					<div slot="reference" class="name-wrapper">
								<el-button type="primary" icon="el-icon-setting" size="mini" @click="editConfig(scope.$index)"></el-button>
							</div>
						
					</template>
				</el-table-column>
				<el-table-column :label="getI18nValue('字段类型')" min-width="180" align="center">
					<template slot-scope="scope">
						<p v-if="scope.row.searchExtConfig">{{ getDictTextByCode('QUALITY_SEARCH_TYPE', JSON.parse(scope.row.searchExtConfig).type) }}</p>
					</template>
				</el-table-column>
				
				<el-table-column  :label="getI18nValue('显示字段')" min-width="150" align="center">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.isShow" active-color="#13ce66" inactive-color="#909399" active-value="Y"
								   inactive-value="N">
						</el-switch>
					</template>
				</el-table-column>
				<el-table-column  :label="getI18nValue('查询字段')" min-width="150" align="center">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.isQuery" active-color="#13ce66" inactive-color="#909399" active-value="Y"
								   inactive-value="N">
						</el-switch>
					</template>
				</el-table-column>
				<el-table-column  :label="getI18nValue('用于规则字段')" min-width="150" align="center">
					<template slot-scope="scope">
						<el-switch :disabled="scope.row.fieldEn =='BEGIN_TIME' ? true : false"  :label="getI18nValue('范围查询')" v-model="scope.row.showAdd" active-color="#13ce66" inactive-color="#909399" active-value="Y"
								   inactive-value="N">
						</el-switch>
					</template>
				</el-table-column>
				
				<el-table-column :label="getI18nValue('字段长度')" min-width="100" align="center">
					<template slot-scope="scope">
						<el-input maxlength="10" v-model="scope.row.fieldLength" disabled="true"></el-input>
					</template>
				</el-table-column>
				
				<el-table-column :label="getI18nValue('操作')"  min-width="180" fixed="right" align="center">
					<template slot-scope="scope">
						<el-button type="primary" icon="el-icon-top" @click="moveUp(scope.$index,scope.row)" @dblclick.native="changeIndex(scope.$index)" circle size="mini"></el-button>
						<el-button type="primary" icon="el-icon-bottom" @click="moveDown(scope.$index,scope.row)" @dblclick.native="changeIndex(scope.$index)"	 circle size="mini"></el-button>
					</template>
				</el-table-column>
			</el-table>
		</template>
	</div>

</div>
</body>



<script src="/easitline-static/js/jquery.min.js"></script>
<script src="/easitline-static/lib/layer/layer.js"></script>
<script type="text/javascript" src="/cc-quality/static/js/base.js"></script>
<script src="/cc-base/static/lib/vue.min.js"></script>
<script src="/cc-base/static/lib/element/element-ui.js"></script>
<script src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
<script type="text/javascript" src="/cc-base/static/js/yq/extends.js"></script>
<script type="text/javascript" src="/cc-quality/static/js/my_i18n.js"></script>
<script type="text/javascript" src="/cc-base/static/js/i18n.js"></script>
<script type="text/javascript">
	var fields = new Vue({
		el: '#fields',
		data() {
			return {
				tables:[],
				fields: [],
				busiType: "",
				tempId: '',
				tableH: 400,
				formLabelWidth: '120px',
				tableDict:{},
				form: {
					TEMPLATE_NAME: '',
			        TEMPLATE_DESC: '',	 			        
			        CALLBACK: '',
			        CALLBACK_URL: '',	 
			        BUSI_TYPE: '',	 
			    },
				busiTypeList: [],
				callBackList: [],
			    rules: {
			    	TEMPLATE_NAME: [
			            { required: true, message: '请输入模板名称', trigger: 'blur' },
			          ],
			          BUSI_TYPE: [
			            { required: true, message: '请选择业务类型', trigger: 'change' }
			          ],
			       }
			}
			
			
		},
		methods: {
			getTemplateInfo(callBack){
				//根据id获取工单数据
				var data={
					'table.ID':this.tempId
				};
				httpAjax('/cc-quality/webcall?action=QcThirdPartyDao.tableEdit', 'POST', data, res => {
					if(res.data){
		        		callBack(res.data);
		       		}  
			  });
			},
			
			getTempFields() {
				
				var data={
						'table.ID':this.tempId
				};
				httpAjax('/cc-quality/webcall?action=QcThirdPartyDao.getFileds', 'POST', data, function(res) {
					console.log(res)
					this.fields = res.data;
				}.bind(this))
				
			},
			getDict() {
				
					var dataJson = {
	                    "controls":["common.getDict(CC_BASE_DICT_SF_YN)"],
	                    "params": {}
	                };
	                httpAjax('/cc-quality/webcall', 'POST', dataJson, function(res) {
	                    if (res['common.getDict(CC_BASE_DICT_SF_YN)']) {
	                    	this.callBackList  = turnArray(res['common.getDict(CC_BASE_DICT_SF_YN)']['data']);
	                    }
	                }.bind(this))
	                
	                
	                var dataJson = {
	                    "controls":["common.getDict(THIRD_PARTY_BUSI_TYPE)"],
	                    "params": {}
	                };
	                httpAjax('/cc-quality/webcall', 'POST', dataJson, function(res) {
	                    if (res['common.getDict(THIRD_PARTY_BUSI_TYPE)']) {
	                    	this.busiTypeList  = turnArray(res['common.getDict(THIRD_PARTY_BUSI_TYPE)']['data']);
	                    }
	                }.bind(this))
	                
	                
			},
			moveUp(index, row) {
				var that = this
				if (index > 0) {
					const upDate = that.fields[index - 1]
					that.fields.splice(index - 1, 1)
					that.fields.splice(index, 0, upDate)
				} else {
					that.$message({
						message: getI18nValue('已经是第一条，不可上移'),
						type: 'warning'
					});
				}
			},
			// 下移
			moveDown(index, row) {
				var that = this
				// console.log('下移', index, row)
				if ((index + 1) === that.fields.length) {
					that.$message({
						message: getI18nValue('已经是最后一条，不可下移'),
						type: 'warning'
					});
				} else {
					const downDate = that.fields[index + 1]
					that.fields.splice(index + 1, 1)
					that.fields.splice(index, 0, downDate)
				}
			},
			// 重置table高度
			resetHeight() {
				return new Promise((resolve, reject) => {
					this.tableH = 400
					resolve()
				})
			},
			saveConfig(index, item) {
				//console.log(("item:"+item))
				//console.log(("fields:"+this.fields[index]))
				this.fields[index].columnType = item.columnType
				this.fields[index].jsonEx = item.jsonEx
			},
			// 设置table高度
			fetTableHeight() {
				this.resetHeight().then(res => {
					this.tableH = this.$refs.tableWrapper.getBoundingClientRect().height - 210
				})
			},
			changeIndex(index){
				var that = this;
				this.$prompt(getI18nValue('排序'), getI18nValue('提示'), {
					confirmButtonText: getI18nValue('确定'),
					cancelButtonText: getI18nValue('取消'),
					inputPattern: /^[0-9]*$/,
					inputErrorMessage: '格式不正确'
				}).then(({ value }) => {
					if(value < 0 || value > this.fields.length){
						this.$message({
							message: getI18nValue('操作失败'),
							type: 'warning'
						});
					}else{
						const downDate = that.fields[index]
						that.fields.splice(index, 1)
						that.fields.splice(value-1, 0, downDate)
					}
				})
			},
			submit(formName) {
				this.$refs[formName].validate((valid) => {
					if (valid) {
	
						this.sortFields()
						var data = {};
						data['table.ID'] = this.tempId;
						
						data['table.TEMPLATE_NAME'] = this.form.TEMPLATE_NAME;
						data['table.TEMPLATE_DESC'] = this.form.TEMPLATE_DESC;
						data['table.CALLBACK'] = this.form.CALLBACK;
						
						data['table.CALLBACK_URL'] = this.form.CALLBACK_URL;
						data['table.BUSI_TYPE'] = this.form.BUSI_TYPE;
	
						data.dataList = this.fields;

						console.log(data);
						
						var url = "/cc-quality/servlet/qcThirdParty?action=";
						    
						if(this.tempId!=null&&this.tempId!=''){
							url =url+"updateTemplate";
						}else{
							url =url+"buildTemplate";
						}
						
						httpAjax(url, 'POST', data, function(result) {
							var state = result.state;
							if (state == '1') {
								this.$message({
									message: getI18nValue('操作成功'),
									type: 'success'
								});
							
							} else {
								this.$alert(getI18nValue(result.message), getI18nValue('提示'), {
									confirmButtonText: getI18nValue('确定')
								});
							}
						}.bind(this))

						top.popup.closeTab(this.option);
						
					} else {
						return false;
					}
					
					
					
					
				});

			},
			sortFields() {
				let list = this.fields;
				for (let i = 0; i < list.length; i++) {
					list[i].SORT = i;
				}
				return list;
			},
			editConfig(index) {
				popup.layerShow({
					type: 2,
					title: (getI18nValue('编辑')),
					offset: '20px',
					area: ['650px;', '550px']
				}, "./configQuery.html", {
					index: index
				});
			},
		},
		created() {
			
				

			var urlObj = getQueryObject()
			console.log(urlObj)
			this.tempId = urlObj['ID'];
			this.getTemplateInfo(res=>{
				console.log('res:')
				console.log(res)
				this.form.BUSI_TYPE = res.BUSI_TYPE,
				this.form.CALLBACK = res.CALLBACK,
				this.form.CALLBACK_URL = res.CALLBACK_URL,
				this.form.TEMPLATE_DESC = res.TEMPLATE_DESC,
				this.form.TEMPLATE_NAME = res.TEMPLATE_NAME
			});		
			this.getTempFields();
			this.getDict();
		},
		mounted() {
			this.fetTableHeight();
		},
		watch: {
			// tableData是el-table绑定的数据
			tempId: {
				// 解决表格显示错位问题
				handler () {
					this.$nextTick(() => {
						// table是el-table绑定的ref属性值
						this.$refs.table.doLayout() // 对 Table 进行重新布局
					})
				},
				deep: true
			},
		}
	})
</script>
</html>
